<template>
  <div class="mb-8 bg-white rounded-lg p-6 shadow-sm">
    <!-- Section Title -->
    <h3 class="text-red-600 font-bold text-lg mb-4">{{ title }}</h3>
    
    <!-- Products List -->
    <div v-if="products && products.length > 0" class="space-y-4">
      <div 
        v-for="(product, index) in products" 
        :key="index"
        class="border-b border-gray-200 last:border-b-0 pb-4 last:pb-0"
      >
        <h4 class="font-semibold text-gray-800 mb-2">{{ product.name }}</h4>
        <p class="text-gray-600 text-sm">{{ product.description || 'No description available' }}</p>
      </div>
    </div>
    
    <!-- Empty State -->
    <div v-else class="text-gray-500 text-center py-8">
      <p>No products available</p>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from "vue";

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  products: {
    type: Array,
    default: () => [],
    validator: (value) => {
      // Each product should have name and optionally description
      return value.every((product) => "name" in product);
    },
  },
});
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
