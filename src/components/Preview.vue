<template>
  <!-- Preview Overlay -->
  <Transition name="preview">
    <div v-if="isVisible" class="fixed inset-0 z-50 flex">
      <!-- Background blur overlay -->
      <div
        class="absolute inset-0 !bg-opacity-30 backdrop-blur-sm"
        @click="closePreview"
      ></div>
      <!-- Preview Panel -->
      <div
        class="ml-auto w-[65%] bg-[#C62027] h-full relative !rounded-t-4xl flex flex-col"
      >
        <!-- Red Header -->
        <div class="bg-[#C62027] text-white p-6 py-10 flex-shrink-0">
          <h1 class="text-xl font-semibold ml-20">
            Force Medicines - Company Overview
          </h1>
          <!-- Close Button -->
          <button
            @click="closePreview"
            class="absolute top-6 right-6 text-white hover:text-gray-200 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
        <!-- Content Area with proper scrolling -->
        <div
          class="bg-gray-200 flex-1 overflow-hidden !rounded-t-4xl flex flex-col"
        >
          <!-- Navigation Tabs -->
          <div class="bg-gray-200 px-6 pt-4 flex-shrink-0">
            <nav class="flex !space-x-12 ml-20">
              <button
                v-for="tab in tabs"
                :key="tab.key"
                @click="activeTab = tab.key"
                class="relative pb-3 text-sm font-medium transition-colors"
                :class="[
                  activeTab === tab.key
                    ? 'border-b-2 border-red-500 text-red-500'
                    : 'text-gray-600 hover:text-gray-800',
                ]"
              >
                {{ tab.label }}
              </button>
            </nav>
          </div>

          <!-- Scrollable Tab Content -->
          <div class="flex-1 overflow-y-auto">
            <div class="px-6 py-6 ml-20">
              <div v-if="activeTab === 'Local Plant'"></div>
              <div v-if="activeTab === 'Production'">
                <PreviewSection title="Emplacement" :data="locationData" />
                <PreviewProductionSection
                  title="Production mondiale"
                  :data="productionData"
                />
                <PreviewProductsSection
                  title="Principaux produits"
                  :products="productsData"
                />
              </div>
              <!-- Other tab content will go here -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </Transition>
</template>
<script setup>
import { ref, defineProps, defineEmits, onMounted, computed, watch } from "vue";
import PreviewSection from "./PreviewSection.vue";
import PreviewProductionSection from "./PreviewProductionSection.vue";
import PreviewProductsSection from "./PreviewProductsSection.vue";
import PreviewClientsSection from "./PreviewClientsSection.vue";
import organisationService from "../Services/services/organisation";
import productsService from "../Services/services/products";
import revenueService from "../Services/services/revenue";

const props = defineProps({
  isVisible: {
    type: Boolean,
    default: false,
  },
  organizationId: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["close"]);

// Tab management
const activeTab = ref("Local Plant");
const tabs = [
  { label: "Local Plant", key: "Local Plant" },
  { label: "Production", key: "Production" },
  { label: "Employee", key: "Employee" },
  { label: "Autres", key: "Autres" },
];

// Organization data
const organization = ref(null);
const products = ref(null);
const revenue = ref(null);
const loading = ref(false);
const error = ref(null);

// Fetch organization data
const fetchOrganizationData = async () => {
  if (!props.organizationId) return;

  loading.value = true;
  error.value = null;

  try {
    const response = await organisationService.getOrganization(
      props.organizationId
    );
    organization.value = response.data;
  } catch (err) {
    console.error("Error fetching organization data:", err);
    error.value = err.message || "Failed to fetch organization data";
  } finally {
    loading.value = false;
  }
};

// Fetch products data
const fetchProductsData = async () => {
  if (!props.organizationId) return;

  try {
    const response = await productsService.getProducts(props.organizationId);
    products.value = response.data;
  } catch (err) {
    console.error("Error fetching products data:", err);
  }
};

// Fetch revenue data
const fetchRevenueData = async () => {
  if (!props.organizationId) return;

  try {
    const response = await revenueService.getRevenue(props.organizationId);
    revenue.value = response.data;
  } catch (err) {
    console.error("Error fetching revenue data:", err);
  }
};

console.log(organization.value);

// Computed property for location data
const locationData = computed(() => {
  if (!organization.value) {
    // Return default data for preview if no organization data is available
    return [
      { label: "HQ", value: "Tunis, Ariana" },
      { label: "R&D", value: "Ariana, Tunisia" },
    ];
  }

  return [
    {
      label: "HQ",
      value:
        organization.value.rAndDSites &&
        organization.value.rAndDSites.length > 0
          ? organization.value.rAndDSites[0].name
          : "No R&D site available",
    },
    {
      label: "R&D",
      value:
        organization.value.otherLocations &&
        organization.value.otherLocations.length > 0
          ? organization.value.otherLocations[0].name
          : "No other location available",
    },
  ];
});

// Computed property for production data
const productionData = computed(() => {
  if (!products.value || !products.value.foreignImplantationSites) {
    // Return default data for preview if no products data is available
    return {
      EU: "1",
      ASIA: "-",
      USA: "-",
      OTHERS: "-",
      Total: "1",
    };
  }

  // Transform foreignImplantationSites into regional data
  const sites = products.value.foreignImplantationSites;
  if (sites.length === 0) {
    return {
      EU: "1",
      ASIA: "-",
      USA: "-",
      OTHERS: "-",
      Total: "1",
    };
  }

  // Initialize counters for each region
  const regionData = {
    EU: 0,
    ASIA: 0,
    USA: 0,
    OTHERS: 0,
  };

  // Process each site and categorize by region based on country name
  sites.forEach((site) => {
    const country = site.name?.toLowerCase() || "";
    const capacity = parseInt(site.capacity) || 1;

    // Simple region mapping based on country names
    if (
      country.includes("france") ||
      country.includes("germany") ||
      country.includes("italy") ||
      country.includes("spain") ||
      country.includes("uk") ||
      country.includes("europe")
    ) {
      regionData.EU += capacity;
    } else if (
      country.includes("china") ||
      country.includes("japan") ||
      country.includes("korea") ||
      country.includes("india") ||
      country.includes("asia")
    ) {
      regionData.ASIA += capacity;
    } else if (
      country.includes("usa") ||
      country.includes("america") ||
      country.includes("united states")
    ) {
      regionData.USA += capacity;
    } else {
      regionData.OTHERS += capacity;
    }
  });

  // Calculate total
  const total =
    regionData.EU + regionData.ASIA + regionData.USA + regionData.OTHERS;

  return {
    EU: regionData.EU > 0 ? regionData.EU.toString() : "-",
    ASIA: regionData.ASIA > 0 ? regionData.ASIA.toString() : "-",
    USA: regionData.USA > 0 ? regionData.USA.toString() : "-",
    OTHERS: regionData.OTHERS > 0 ? regionData.OTHERS.toString() : "-",
    Total: total > 0 ? total.toString() : "-",
  };
});

// Computed property for products data
const productsData = computed(() => {
  if (!products.value || !products.value.products) {
    // Return default data for preview if no products data is available
    return [
      {
        name: "Product_name",
        description: "Product Description can be put here",
        image: null, // Will show placeholder image
      },
      {
        name: "Product_name",
        description: "Product Description can be put here",
        image: null, // Will show placeholder image
      },
      {
        name: "Product_name",
        description: "Product Description can be put here",
        image: null, // Will show placeholder image
      },
    ];
  }

  // Return the products array with name, description, and image
  return products.value.products.map((product) => ({
    name: product.name || "Product_name",
    description: product.description || "Product Description can be put here",
    image: product.image || null, // Add image support
  }));
});

// Computed property for clients data
const clientsData = computed(() => {
  if (!revenue.value || !revenue.value.clientsTypes) {
    // Return default data for preview if no revenue data is available
    return [
      { example: "Client A", description: "Major pharmaceutical client" },
      { example: "Client B", description: "Healthcare provider" },
    ];
  }

  // Return the clientsTypes array with example field
  return revenue.value.clientsTypes.map((client) => ({
    example: client.example || "Client Name",
    description: client.description || "Client information",
  }));
});

// Watch for visibility changes to fetch data when the preview is opened
onMounted(() => {
  if (props.isVisible && props.organizationId) {
    fetchOrganizationData();
    fetchProductsData();
    fetchRevenueData();
  }
});

// Watch for changes in visibility
watch(
  () => props.isVisible,
  (newValue) => {
    if (newValue && props.organizationId) {
      fetchOrganizationData();
      fetchProductsData();
      fetchRevenueData();
    }
  }
);

// Close preview function
const closePreview = () => {
  emit("close");
};
</script>
<style scoped>
/* Preview slide-in animation */
.preview-enter-active,
.preview-leave-active {
  transition: all 0.3s ease;
}
.preview-enter-from {
  transform: translateX(100%);
  opacity: 0;
}
.preview-leave-to {
  transform: translateX(100%);
  opacity: 0;
}
/* Ensure the panel slides in from the right */
.preview-enter-active .ml-auto,
.preview-leave-active .ml-auto {
  transition: transform 0.3s ease;
}
.preview-enter-from .ml-auto {
  transform: translateX(100%);
}
.preview-leave-to .ml-auto {
  transform: translateX(100%);
}
</style>
