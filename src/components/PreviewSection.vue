<template>
  <div class="mb-8 bg-white rounded-lg p-6 shadow-sm">
    <!-- Section Title -->
    <h3 class="text-red-600 font-bold text-lg mb-4">{{ title }}</h3>

    <!-- Data Table -->
    <table class="w-full">
      <tbody>
        <tr
          v-for="(item, index) in data"
          :key="index"
          class="border-b border-gray-200 last:border-b-0"
        >
          <td class="py-4 font-medium text-gray-700 w-1/3">{{ item.label }}</td>
          <td class="py-4 text-gray-800">{{ item.value }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
import { defineProps } from "vue";

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  data: {
    type: Array,
    default: () => [],
    validator: (value) => {
      // Each item should have label and value properties
      return value.every((item) => "label" in item && "value" in item);
    },
  },
});
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
