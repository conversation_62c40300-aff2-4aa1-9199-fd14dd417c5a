<template>
  <div class="mb-8 bg-white rounded-lg px-4 py-4 shadow-sm mr-20">
    <!-- Section Title -->
    <h3
      class="!text-[#C62027] border-b border-gray-400 !font-bold !text-2xl mb-4 pb-2"
    >
      {{ title }}
    </h3>

    <!-- Data Table -->
    <table class="w-full ml-30 !font-semibold">
      <tbody>
        <tr
          v-for="(item, index) in data"
          :key="index"
          class="border-b border-gray-200 last:border-b-0"
        >
          <td class="py-4 text-gray-700 !font-semibold w-1/3">
            {{ item.label }}
          </td>
          <td class="py-4 text-gray-600 !font-semibold">{{ item.value }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
import { defineProps } from "vue";

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  data: {
    type: Array,
    default: () => [],
    validator: (value) => {
      // Each item should have label and value properties
      return value.every((item) => "label" in item && "value" in item);
    },
  },
});
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
