<template>
  <div class="mb-8 bg-white rounded-lg p-6 shadow-sm">
    <!-- Section Title -->
    <h3
      class="!text-[#C62027] border-b border-gray-400 pb-2 font-bold text-lg mb-4"
    >
      {{ title }}
    </h3>

    <!-- Production Data Table -->
    <table class="w-full">
      <thead>
        <tr class="!border-b !border-gray-300">
          <th class="py-4 text-center font-medium text-gray-700">EU</th>
          <th class="py-4 text-center font-medium text-gray-700">ASIA</th>
          <th class="py-4 text-center font-medium text-gray-700">USA</th>
          <th class="py-4 text-center font-medium text-gray-700">OTHERS</th>
          <th class="py-4 text-center font-medium text-gray-700">Total</th>
        </tr>
      </thead>
      <tbody>
        <tr class="!border-b !border-gray-300">
          <td class="py-4 text-center text-gray-800">{{ data.EU || "-" }}</td>
          <td class="py-4 text-center text-gray-800">{{ data.ASIA || "-" }}</td>
          <td class="py-4 text-center text-gray-800">{{ data.USA || "-" }}</td>
          <td class="py-4 text-center text-gray-800">
            {{ data.OTHERS || "-" }}
          </td>
          <td class="py-4 text-center text-gray-800">
            {{ data.Total || "-" }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
import { defineProps } from "vue";

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  data: {
    type: Object,
    default: () => ({
      EU: "-",
      ASIA: "-",
      USA: "-",
      OTHERS: "-",
      Total: "-",
    }),
  },
});
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
