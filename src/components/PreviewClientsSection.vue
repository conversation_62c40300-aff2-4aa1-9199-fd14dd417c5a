<template>
  <div class="mb-8 bg-white rounded-lg p-6 shadow-sm">
    <!-- Section Title -->
    <h3 class="text-red-600 font-bold text-lg mb-6">{{ title }}</h3>

    <!-- Clients Grid - 2 columns -->
    <div v-if="clients && clients.length > 0" class="grid grid-cols-2 gap-4">
      <div
        v-for="(client, index) in clients"
        :key="index"
        class="bg-gray-100 rounded-lg p-4 text-center"
      >
        <h4 class="font-medium text-gray-800">
          {{ client.example || client.name }}
        </h4>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="text-gray-500 text-center py-8">
      <p>No clients available</p>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from "vue";

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  clients: {
    type: Array,
    default: () => [],
    validator: (value) => {
      // Each client should have example field (primary) or name field (fallback)
      return value.every((client) => "example" in client || "name" in client);
    },
  },
});
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
