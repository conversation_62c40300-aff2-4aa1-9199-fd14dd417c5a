<template>
  <div class="mb-8 bg-white rounded-lg p-6 shadow-sm">
    <!-- Section Title -->
    <h3 class="text-red-600 font-bold text-lg mb-6">{{ title }}</h3>
    
    <!-- Clients List -->
    <div v-if="clients && clients.length > 0" class="space-y-4">
      <div 
        v-for="(client, index) in clients" 
        :key="index"
        class="border border-gray-300 rounded-lg p-4 bg-white"
      >
        <h4 class="font-semibold text-gray-800 mb-2">{{ client.name || client.example }}</h4>
        <p class="text-gray-500 text-sm">{{ client.description || 'Client information' }}</p>
      </div>
    </div>
    
    <!-- Empty State -->
    <div v-else class="text-gray-500 text-center py-8">
      <p>No clients available</p>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from "vue";

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  clients: {
    type: Array,
    default: () => [],
    validator: (value) => {
      // Each client should have example or name field
      return value.every((client) => "example" in client || "name" in client);
    },
  },
});
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
