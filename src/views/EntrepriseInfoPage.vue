<template class="!bg-[#EEF0F4]">
  <!-- Outer flex container to reserve 10% for future menu -->
  <div class="flex min-h-screen" :class="{ 'blur-sm': showPreview }">
    <!-- 10% for menu (empty placeholder for now) -->
    <!-- <NavBar /> -->
    <aside class="w-[10%]">
      <SideMenu :menuItems="customMenuItems" />
    </aside>

    <main class="w-[90%] px-4 py-6">
      <CompanyHeader
        :imageURL="imageUrl"
        :companyName="companyName"
        :country="country"
        :foundationYear="foundationYear"
        :website="website"
        :progressValue="progressValue"
        @open-preview="showPreview = true"
      />

      <router-view :key="$route.fullPath" />
    </main>
  </div>

  <!-- Preview Component -->
  <Preview :isVisible="showPreview" @close="showPreview = false" />
</template>
<script setup>
import { ref, onMounted, computed } from "vue";
import CompanyHeader from "@/components/CompanyHeader.vue";
import SideMenu from "@/components/SideMenu.vue";
import Preview from "@/components/Preview.vue";
import organizationService from "../Services/services/organisation";
import { useRoute } from "vue-router";

const route = useRoute();
const organization = ref(null);
const organizationId = route.params.organizationId;
const showPreview = ref(false);
onMounted(async () => {
  try {
    const response = await organizationService.getOrganization(organizationId);

    organization.value = response.data;
  } catch (error) {
    console.error("Failed to fetch organization data:", error);
  }
});

const companyName = computed(() => {
  return organization.value ? organization.value.name : "Loading...";
});
const imageUrl = computed(() => {
  // Adjust if your organization data includes a logo URL.

  return organization.value && organization.value.logoUrl
    ? organization.value.logoUrl
    : "https://via.placeholder.com/150";
});
const country = computed(() => {
  return organization.value ? organization.value.country : "N/A";
});
const foundationYear = computed(() => {
  return organization.value ? organization.value.foundingYear : "N/A";
});
const website = computed(() => {
  return organization.value ? organization.value.websiteUrl : "#";
});
const progressValue = computed(() => {
  return organization.value ? organization.value.completion : 0;
});
const customMenuItems = [
  {
    name: "EntrepriseInfo",
    label: "Entreprise Info",
    path: `/entrepriseinfo/${organizationId}`,
    icon: '<path d="M8.33333 10.8333H3.33333C3.11232 10.8333 2.90036 10.9211 2.74408 11.0774C2.5878 11.2337 2.5 11.4457 2.5 11.6667V16.6667C2.5 16.8877 2.5878 17.0996 2.74408 17.2559C2.90036 17.4122 3.11232 17.5 3.33333 17.5H8.33333C8.55435 17.5 8.76631 17.4122 8.92259 17.2559C9.07887 17.0996 9.16667 16.8877 9.16667 16.6667V11.6667C9.16667 11.4457 9.07887 11.2337 8.92259 11.0774C8.76631 10.9211 8.55435 10.8333 8.33333 10.8333ZM7.5 15.8333H4.16667V12.5H7.5V15.8333ZM16.6667 2.5H11.6667C11.4457 2.5 11.2337 2.5878 11.0774 2.74408C10.9211 2.90036 10.8333 3.11232 10.8333 3.33333V8.33333C10.8333 8.55435 10.9211 8.76631 11.0774 8.92259C11.2337 9.07887 11.4457 9.16667 11.6667 9.16667H16.6667C16.8877 9.16667 17.0996 9.07887 17.2559 8.92259C17.4122 8.76631 17.5 8.55435 17.5 8.33333V3.33333C17.5 3.11232 17.4122 2.90036 17.2559 2.74408C17.0996 2.5878 16.8877 2.5 16.6667 2.5V2.5ZM15.8333 7.5H12.5V4.16667H15.8333V7.5ZM16.6667 13.3333H15V11.6667C15 11.4457 14.9122 11.2337 14.7559 11.0774C14.5996 10.9211 14.3877 10.8333 14.1667 10.8333C13.9457 10.8333 13.7337 10.9211 13.5774 11.0774C13.4211 11.2337 13.3333 11.4457 13.3333 11.6667V13.3333H11.6667C11.4457 13.3333 11.2337 13.4211 11.0774 13.5774C10.9211 13.7337 10.8333 13.9457 10.8333 14.1667C10.8333 14.3877 10.9211 14.5996 11.0774 14.7559C11.2337 14.9122 11.4457 15 11.6667 15H13.3333V16.6667C13.3333 16.8877 13.4211 17.0996 13.5774 17.2559C13.7337 17.4122 13.9457 17.5 14.1667 17.5C14.3877 17.5 14.5996 17.4122 14.7559 17.2559C14.9122 17.0996 15 16.8877 15 16.6667V15H16.6667C16.8877 15 17.0996 14.9122 17.2559 14.7559C17.4122 14.5996 17.5 14.3877 17.5 14.1667C17.5 13.9457 17.4122 13.7337 17.2559 13.5774C17.0996 13.4211 16.8877 13.3333 16.6667 13.3333ZM8.33333 2.5H3.33333C3.11232 2.5 2.90036 2.5878 2.74408 2.74408C2.5878 2.90036 2.5 3.11232 2.5 3.33333V8.33333C2.5 8.55435 2.5878 8.76631 2.74408 8.92259C2.90036 9.07887 3.11232 9.16667 3.33333 9.16667H8.33333C8.55435 9.16667 8.76631 9.07887 8.92259 8.92259C9.07887 8.76631 9.16667 8.55435 9.16667 8.33333V3.33333C9.16667 3.11232 9.07887 2.90036 8.92259 2.74408C8.76631 2.5878 8.55435 2.5 8.33333 2.5V2.5ZM7.5 7.5H4.16667V4.16667H7.5V7.5Z" fill="#C62027"/>',
  },
];
</script>
